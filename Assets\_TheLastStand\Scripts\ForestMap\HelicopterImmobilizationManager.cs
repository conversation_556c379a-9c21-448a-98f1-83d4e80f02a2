using UnityEngine;
using Mirror;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Manages complete player immobilization during helicopter intro sequences.
/// Extends the existing constraint system to disable all movement, input, and physics.
/// Ensures players remain completely stationary and locked in position during the helicopter ride.
/// </summary>
public class HelicopterImmobilizationManager : NetworkBehaviour
{
    public static HelicopterImmobilizationManager Instance { get; private set; }

    [Header("Immobilization Configuration")]
    [SerializeField] private bool enableImmobilizationDuringFlight = true;
    [SerializeField] private bool disableImmobilizationOnLanding = true;
    [SerializeField] private bool disableImmobilizationOnExit = true;
    [SerializeField] private float activationDelay = 0.5f;
    [SerializeField] private float deactivationDelay = 1.0f;

    [Header("Debug Settings")]
    [SerializeField] private bool showDebugLogs = true;

    [Header("Helicopter References")]
    [SerializeField] private ForestIntroHelicopter helicopter;
    [SerializeField] private HelicopterExitManager exitManager;

    // Global immobilization state
    [SyncVar(hook = nameof(OnGlobalImmobilizationChanged))]
    private bool globalImmobilizationActive = false;

    // Registered players for immobilization
    private List<HelicopterPlayerImmobilizer> registeredImmobilizers = new List<HelicopterPlayerImmobilizer>();

    // State tracking
    private bool immobilizationCurrentlyActive = false;
    private Helicopter.HelicopterState lastHelicopterState = Helicopter.HelicopterState.None;

    public bool GlobalImmobilizationActive => globalImmobilizationActive;

    private void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
        }
        else if (Instance != this)
        {
            Debug.LogWarning("HelicopterImmobilizationManager: Multiple instances detected. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
    }

    public override void OnStartServer()
    {
        base.OnStartServer();
        InitializeImmobilizationManager();
    }

    public override void OnStartClient()
    {
        base.OnStartClient();
        if (!isServer)
        {
            InitializeImmobilizationManager();
        }
    }

    private void InitializeImmobilizationManager()
    {
        // Auto-find helicopter if not assigned
        if (helicopter == null)
        {
            helicopter = FindObjectOfType<ForestIntroHelicopter>();
            if (helicopter != null && showDebugLogs)
            {
                Debug.Log("HelicopterImmobilizationManager: Auto-found ForestIntroHelicopter");
            }
        }

        // Auto-find exit manager if not assigned
        if (exitManager == null)
        {
            exitManager = FindObjectOfType<HelicopterExitManager>();
            if (exitManager != null && showDebugLogs)
            {
                Debug.Log("HelicopterImmobilizationManager: Auto-found HelicopterExitManager");
            }
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterImmobilizationManager: Initialized with helicopter: {(helicopter != null ? helicopter.name : "None")}, " +
                     $"exit manager: {(exitManager != null ? exitManager.name : "None")}");
        }
    }

    private void Update()
    {
        if (!isServer) return;

        // Monitor helicopter state changes
        if (helicopter != null)
        {
            Helicopter.HelicopterState currentState = helicopter.currentState;
            if (currentState != lastHelicopterState)
            {
                HandleHelicopterStateChange(currentState);
                lastHelicopterState = currentState;
            }
        }
    }

    [Server]
    private void HandleHelicopterStateChange(Helicopter.HelicopterState newState)
    {
        if (!isServer) return;

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterImmobilizationManager: Helicopter state changed to {newState}");
        }

        switch (newState)
        {
            case Helicopter.HelicopterState.InitializingPath:
            case Helicopter.HelicopterState.MovingToWaypoint:
            case Helicopter.HelicopterState.Rotating:
                if (enableImmobilizationDuringFlight && !GlobalImmobilizationActive)
                {
                    StartCoroutine(ActivateImmobilizationWithDelay());
                }
                break;

            case Helicopter.HelicopterState.Landing:
                if (disableImmobilizationOnLanding && GlobalImmobilizationActive)
                {
                    StartCoroutine(DeactivateImmobilizationWithDelay());
                }
                break;

            case Helicopter.HelicopterState.PathComplete:
                if (GlobalImmobilizationActive)
                {
                    StartCoroutine(DeactivateImmobilizationWithDelay());
                }
                break;

            case Helicopter.HelicopterState.None: // Idle
                // Keep current immobilization state during none
                break;
        }
    }

    /// <summary>
    /// Registers a player immobilizer with the manager
    /// </summary>
    public void RegisterPlayerImmobilizer(HelicopterPlayerImmobilizer immobilizer)
    {
        if (immobilizer == null) return;

        if (!registeredImmobilizers.Contains(immobilizer))
        {
            registeredImmobilizers.Add(immobilizer);

            // Apply current global immobilization state to newly registered player
            try
            {
                if (isServer && GlobalImmobilizationActive && immobilizer.HasValidSpawnPoint)
                {
                    immobilizer.ActivateImmobilization();
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"HelicopterImmobilizationManager: Failed to activate immobilization for newly registered player {immobilizer.gameObject.name}: {ex.Message}");
            }

            if (showDebugLogs)
            {
                Debug.Log($"HelicopterImmobilizationManager: Registered immobilizer for {immobilizer.gameObject.name}. Total: {registeredImmobilizers.Count}");
            }
        }
    }

    /// <summary>
    /// Unregisters a player immobilizer from the manager
    /// </summary>
    public void UnregisterPlayerImmobilizer(HelicopterPlayerImmobilizer immobilizer)
    {
        if (immobilizer == null) return;

        if (registeredImmobilizers.Contains(immobilizer))
        {
            registeredImmobilizers.Remove(immobilizer);

            if (showDebugLogs)
            {
                Debug.Log($"HelicopterImmobilizationManager: Unregistered immobilizer for {immobilizer.gameObject.name}. Total: {registeredImmobilizers.Count}");
            }
        }
    }

    [Server]
    private IEnumerator ActivateImmobilizationWithDelay()
    {
        if (!isServer) yield break;

        yield return new WaitForSeconds(activationDelay);

        if (showDebugLogs)
        {
            Debug.Log("HelicopterImmobilizationManager: Activating immobilization for all players");
        }

        ActivateAllImmobilization();
    }

    [Server]
    private IEnumerator DeactivateImmobilizationWithDelay()
    {
        if (!isServer) yield break;

        yield return new WaitForSeconds(deactivationDelay);

        if (showDebugLogs)
        {
            Debug.Log("HelicopterImmobilizationManager: Deactivating immobilization for all players");
        }

        DeactivateAllImmobilization();
    }

    [Server]
    public void ActivateAllImmobilization()
    {
        if (!isServer) return;

        globalImmobilizationActive = true;

        foreach (var immobilizer in registeredImmobilizers)
        {
            if (immobilizer != null && immobilizer.HasValidSpawnPoint)
            {
                try
                {
                    immobilizer.ActivateImmobilization();
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"HelicopterImmobilizationManager: Failed to activate immobilization for {immobilizer.gameObject.name}: {ex.Message}");
                }
            }
        }
    }

    [Server]
    public void DeactivateAllImmobilization()
    {
        if (!isServer) return;

        globalImmobilizationActive = false;

        foreach (var immobilizer in registeredImmobilizers)
        {
            if (immobilizer != null)
            {
                try
                {
                    immobilizer.DeactivateImmobilization();
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"HelicopterImmobilizationManager: Failed to deactivate immobilization for {immobilizer.gameObject.name}: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// Called by HelicopterExitManager when exit sequence begins
    /// </summary>
    [Server]
    public void OnHelicopterExitSequenceStarted()
    {
        if (!isServer) return;

        if (disableImmobilizationOnExit && GlobalImmobilizationActive)
        {
            if (showDebugLogs)
            {
                Debug.Log("HelicopterImmobilizationManager: Exit sequence started, deactivating immobilization");
            }
            DeactivateAllImmobilization();
        }
    }

    /// <summary>
    /// Force activate immobilization (for manual control)
    /// </summary>
    [Server]
    public void ForceActivateImmobilization()
    {
        if (!isServer) return;

        if (showDebugLogs)
        {
            Debug.Log("HelicopterImmobilizationManager: Force activating immobilization");
        }

        ActivateAllImmobilization();
    }

    /// <summary>
    /// Force deactivate immobilization (for manual control)
    /// </summary>
    [Server]
    public void ForceDeactivateImmobilization()
    {
        if (!isServer) return;

        if (showDebugLogs)
        {
            Debug.Log("HelicopterImmobilizationManager: Force deactivating immobilization");
        }

        DeactivateAllImmobilization();
    }

    private void OnGlobalImmobilizationChanged(bool oldValue, bool newValue)
    {
        immobilizationCurrentlyActive = newValue;
        
        if (showDebugLogs)
        {
            Debug.Log($"HelicopterImmobilizationManager: Global immobilization {(immobilizationCurrentlyActive ? "activated" : "deactivated")}");
        }
    }

    /// <summary>
    /// Gets the current immobilization status for debugging
    /// </summary>
    public string GetImmobilizationStatus()
    {
        return $"Global: {GlobalImmobilizationActive}, Registered Players: {registeredImmobilizers.Count}, " +
               $"Helicopter State: {(helicopter != null ? helicopter.currentState.ToString() : "No Helicopter")}";
    }

    private void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }
    }
}
