using UnityEngine;

public class ForestIntroPlayer : Mono<PERSON><PERSON><PERSON><PERSON>
{
    [Header("References")]
    [Tooltip("The transform of the player's visual model. Should be a child of this object.")]
    [SerializeField] private Transform playerModel;
    private Transform cameraTransform;
    private ForestMap_UIManager uiManager;

    [Header("Settings")]
    [SerializeField] private float mouseSensitivity = 200f;
    [SerializeField] private float verticalLookMinAngle = -85f;
    [SerializeField] private float verticalLookMaxAngle = 85f;

    private float _currentCameraXRotation = 0f;
    private readonly Vector3 _cameraLocalOffset = new Vector3(0f, 1.693f, 0.174f);
    private readonly Quaternion _cameraLocalRotation = Quaternion.identity;
    private bool isSettingsPanelOpen = false;

    // Helicopter constraint system
    private HelicopterTransformConstraint helicopterConstraint;

    void Start()
    {
        // Only lock cursor if we're in a gameplay scene, not in MainMenu
        string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        bool isMainMenuScene = currentSceneName.Contains("MainMenu") || currentSceneName.Contains("Menu");
        
        if (!isMainMenuScene)
        {
            Debug.Log($"ForestIntroPlayer: Locking cursor for gameplay scene '{currentSceneName}'");
            LockCursor();
        }
        else
        {
            Debug.Log($"ForestIntroPlayer: NOT locking cursor in menu scene '{currentSceneName}'");
        }

        // Auto-find playerModel if not assigned (for dynamically added components)
        if (playerModel == null)
        {
            // Try to find meshObj from MyClient component
            MyClient myClient = GetComponent<MyClient>();
            if (myClient != null && myClient.MeshObj != null)
            {
                playerModel = myClient.MeshObj.transform;
                Debug.Log("ForestIntroPlayer: Auto-assigned playerModel from MyClient.MeshObj.", this);
            }
            else
            {
                Debug.LogWarning("ForestIntroPlayer: 'playerModel' is not assigned and could not auto-find from MyClient. Visual Z-axis rotation based on yaw will not apply to a separate model.", this);
            }
        }

        // Try to find UI Manager with enhanced scene-aware detection
        uiManager = FindObjectOfType<ForestMap_UIManager>();
        if (uiManager == null)
        {
            // Check if we're in a scene where UI Manager is expected
            // Reuse the already declared variables from the outer scope
            if (isMainMenuScene)
            {
                Debug.Log($"ForestIntroPlayer: ForestMap_UIManager not found in scene '{currentSceneName}'. This is expected in MainMenu scenes.", this);
            }
            else
            {
                Debug.LogWarning($"ForestIntroPlayer: ForestMap_UIManager not found in scene '{currentSceneName}'. ESC to toggle settings panel will not work. Consider adding ForestMap_UIManager to this scene.", this);
            }
        }
        else
        {
            Debug.Log($"ForestIntroPlayer: Successfully found ForestMap_UIManager in scene '{UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}'.", this);
        }

        // Initialize helicopter constraint system
        helicopterConstraint = GetComponent<HelicopterTransformConstraint>();
        if (helicopterConstraint == null)
        {
            Debug.Log("ForestIntroPlayer: No HelicopterTransformConstraint component found. Helicopter constraints will not be applied.", this);
        }
    }

    public void InitializePlayerCamera(Camera cameraToUse)
    {
        if (cameraToUse != null)
        {
            Debug.Log($"ForestIntroPlayer: Configuring assigned camera '{cameraToUse.name}'.", this);
            cameraToUse.transform.SetParent(this.transform);
            cameraToUse.transform.localPosition = _cameraLocalOffset;
            cameraToUse.transform.localRotation = _cameraLocalRotation;
            this.cameraTransform = cameraToUse.transform;
        }
        else
        {
            Debug.LogWarning("ForestIntroPlayer: No specific camera assigned by Manager. Attempting to find and configure Camera.main.", this);
            Camera mainCam = Camera.main;
            if (mainCam != null)
            {
                Debug.Log($"ForestIntroPlayer: Found and configuring Camera.main '{mainCam.name}'.", this);
                mainCam.transform.SetParent(this.transform);
                mainCam.transform.localPosition = _cameraLocalOffset;
                mainCam.transform.localRotation = _cameraLocalRotation;
                this.cameraTransform = mainCam.transform;
            }
            else
            {
                Debug.LogError("ForestIntroPlayer: No camera provided and Camera.main could not be found. Player will not have a camera managed by this script.", this);
            }
        }
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            TogglePanelAndCursor();
        }

        if (!isSettingsPanelOpen)
        {
            HandleMouseLook();
        }
    }

    private void TogglePanelAndCursor()
    {
        if (uiManager != null)
        {
            uiManager.ToggleSettingsPanel();
            isSettingsPanelOpen = !isSettingsPanelOpen;

            if (isSettingsPanelOpen)
            {
                UnlockCursor();
            }
            else
            {
                LockCursor();
            }
        }
        else
        {
            Debug.LogWarning("ForestIntroPlayer: uiManager reference is null. Cannot toggle settings panel.", this);
        }
    }

    private void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity * Time.deltaTime;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity * Time.deltaTime;

        // Apply Y-axis rotation (yaw) with constraint checking
        if (helicopterConstraint != null && helicopterConstraint.ConstraintsActive)
        {
            // Calculate new Y rotation
            float currentY = transform.localEulerAngles.y;
            float newY = currentY + mouseX;

            // Clamp to constraint limits
            float clampedY = helicopterConstraint.ClampYRotation(newY);

            // Apply the constrained rotation
            Vector3 currentEuler = transform.localEulerAngles;
            currentEuler.y = clampedY;
            transform.localEulerAngles = currentEuler;
        }
        else
        {
            // Normal rotation when constraints are not active
            transform.Rotate(Vector3.up * mouseX);
        }

        // Handle camera pitch (X-axis rotation)
        if (cameraTransform != null)
        {
            _currentCameraXRotation -= mouseY;
            _currentCameraXRotation = Mathf.Clamp(_currentCameraXRotation, verticalLookMinAngle, verticalLookMaxAngle);
            cameraTransform.localRotation = Quaternion.Euler(_currentCameraXRotation, 0f, 0f);
        }
    }

    void LateUpdate()
    {
        if (playerModel != null)
        {
            float yawAngle = transform.eulerAngles.y;
            // Apply yaw rotation to Y-axis to keep player upright, not sideways
            // This now works for both separate models AND when playerModel == this.transform (like replicated players)
            playerModel.rotation = Quaternion.Euler(0f, yawAngle, 0f);
        }
    }

    public float GetWorldYRotation()
    {
        return transform.eulerAngles.y;
    }

    /// <summary>
    /// Legacy method name for backward compatibility. Use GetWorldYRotation instead.
    /// </summary>
    [System.Obsolete("Use GetWorldYRotation instead. This method name was misleading.")]
    public float GetWorldZRotation()
    {
        return GetWorldYRotation();
    }

    public Quaternion GetWorldOrientation()
    {
        return transform.rotation;
    }

    public void LockCursor()
    {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    public void UnlockCursor()
    {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }
}
